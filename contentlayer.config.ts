import { defineDocumentType, makeSource } from "contentlayer/source-files"
import type { ComputedFields } from "contentlayer/source-files"

/** 计算字段配置 */
const computedFields: ComputedFields = {
    slug: {
        type: "string",
        resolve: (doc) => `/${doc._raw.flattenedPath}`,
    },
    slugAsParams: {
        type: "string",
        resolve: (doc) => doc._raw.flattenedPath.split("/").slice(1).join("/"),
    },
}

/** 页面文档类型定义 */
export const Page = defineDocumentType(() => ({
    name: "Page",
    filePathPattern: `pages/**/*.mdx`,
    contentType: "mdx",
    fields: {
        title: {
            type: "string",
            required: true,
        },
        description: {
            type: "string",
        },
    },
    computedFields,
}))

/** 博客文章文档类型定义 */
export const Post = defineDocumentType(() => ({
    name: "Post",
    filePathPattern: `posts/**/*.mdx`,
    contentType: "mdx",
    fields: {
        title: {
            type: "string",
            required: true,
        },
        description: {
            type: "string",
        },
        date: {
            type: "date",
            required: true,
        },
        published: {
            type: "boolean",
            default: true,
        },
        tags: {
            type: "list",
            of: { type: "string" },
        },
    },
    computedFields,
}))

/** 内容源配置 */
export default makeSource({
    contentDirPath: "./content",
    documentTypes: [Post, Page],
    mdx: {
        remarkPlugins: [],
        rehypePlugins: [],
    },
})
